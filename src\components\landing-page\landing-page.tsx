import { HtmlHistory } from "../../../utils/types";
import AskAI from "../ask-ai/ask-ai";

interface LandingPageProps {
  html: string;
  setHtml: (html: string) => void;
  isAiWorking: boolean;
  setisAiWorking: React.Dispatch<React.SetStateAction<boolean>>;
  htmlHistory: HtmlHistory[];
  onSuccess: (h: string, p: string, n?: number[][]) => void;
  onNewPrompt: (prompt: string) => void;
  onScrollToBottom: () => void;
  onFirstPrompt: () => void;
}

export default function LandingPage({
  html,
  setHtml,
  isAiWorking,
  setisAiWorking,
  htmlHistory,
  onSuccess,
  onNewPrompt,
  onScrollToBottom,
  onFirstPrompt,
}: LandingPageProps) {

  return (
    <div className="flex-1 flex flex-col items-center justify-center bg-neutral-950 px-6 relative min-h-screen">
      <div className="w-full max-w-4xl text-center mb-16">
        <h1 className="text-6xl lg:text-8xl font-bold text-white mb-6 leading-tight">
          Prompt and then{" "}
          <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
            Witness
          </span>
        </h1>
        <p className="text-xl lg:text-2xl text-neutral-400 font-medium">
          Create apps and websites by chatting with AI
        </p>
      </div>

      {/* Custom positioned AskAI component for landing page */}
      <div className="w-full max-w-3xl mx-auto">
        <AskAI
          html={html}
          setHtml={setHtml}
          isAiWorking={isAiWorking}
          setisAiWorking={setisAiWorking}
          htmlHistory={htmlHistory}
          onSuccess={(h: string, p: string, n?: number[][]) => {
            onFirstPrompt(); // Switch to editor mode
            onSuccess(h, p, n);
          }}
          onNewPrompt={onNewPrompt}
          onScrollToBottom={onScrollToBottom}
          landingMode={true}
        />
      </div>
    </div>
  );
}
